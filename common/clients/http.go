package clients

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"time"
	"ztna_server_auth/common/config"
	"ztna_server_auth/dto"
)

func SendLoginLog(data *dto.LoginAccess) error {
	dataBytes, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("failed to marshal login log data: %w", err)
	}

	req, err := http.NewRequest(http.MethodPost, fmt.Sprintf("%s/log-tracker/v1/identity/logs", config.Config().ThirdParty.LogTrackerAddress), bytes.NewBuffer(dataBytes))
	if err != nil {
		return fmt.Errorf("failed to create HTTP request: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.<PERSON><PERSON><PERSON>("failed to send HTTP request: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return fmt.Errorf("HTTP request failed with status code: %d", resp.StatusCode)
	}

	return nil
}

func Report(loginAccess *dto.LoginAccess) error {

	data := &dto.ReportLogin{
		Type:     "logon",
		UserCode: loginAccess.UserCode,
		ClientId: loginAccess.ClientID,
		OrgName:  loginAccess.ProductID,
	}

	reportData := &dto.LoginReportData{
		ClientActiveTime:     time.Now().Unix(),
		ClientCurrentNetwork: loginAccess.IP,
	}

	bytesByte, _ := json.Marshal(reportData)

	data.Data = string(bytesByte)

	bytesData, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("failed to marshal login log data: %w", err)
	}

	request, err := http.NewRequest(http.MethodPost, fmt.Sprintf("%s/log-tracker/v1/identity/logs", config.Config().ThirdParty.LogTrackerAddress), bytes.NewBuffer(bytesData))
	if err != nil {
		return err
	}
	if err != nil {
		return fmt.Errorf("failed to create HTTP request: %w", err)
	}

	// 设置请求头
	request.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(request)
	if err != nil {
		return fmt.Errorf("failed to send HTTP request: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return fmt.Errorf("HTTP request failed with status code: %d", resp.StatusCode)
	}

	return nil

}
