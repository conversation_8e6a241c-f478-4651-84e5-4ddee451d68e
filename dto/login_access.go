package dto

type LoginAccess struct {
	Action     string `json:"action" bson:"-"`
	OrgCode    string `json:"org_code" bson:"org_code"`
	OrgName    string `json:"org_name" bson:"org_name"`
	ProductID  string `json:"product_id" bson:"product_id"`
	ClientID   string `json:"client_id" bson:"client_id"`
	IP         string `json:"ip" bson:"ip"`
	UserCode   string `json:"user_code" bson:"user_code"`
	UserName   string `json:"user_name" bson:"user_name"`
	LoginTime  int64  `json:"login_time" bson:"login_time"`
	Platform   string `json:"platform" bson:"platform"`
	CreateTime int64  `json:"create_time" bson:"create_time"`
	UA         string `json:"ua" bson:"ua"`
	Device     string `json:"device" bson:"device"`
	Browser    string `json:"browser" bson:"browser"`
	Area       Area   `json:"area" bson:"area"`
	IPPrefix   string `json:"ip_prefix" bson:"ip_prefix"`
}

type Area struct {
	Country string `json:"country" bson:"country"`
	City    string `json:"city" bson:"city"`
}

type LoginAccessAgg struct {
	OrgCode   string `json:"org_code" bson:"org_code"`
	OrgName   string `json:"org_name" bson:"org_name"`
	ProductID string `json:"product_id" bson:"product_id"`

	Type int `json:"type" bson:"type"` // 当1: object 为userCode 2:Object 为ClientID

	Object string `json:"object" bson:"object"`

	AggCount int64 `json:"agg_count" bson:"agg_count"`

	CreatedAt int64 `json:"create_at" bson:"create_at"`
	UpdatedAt int64 `json:"update_at" bson:"update_at"`
}

type BusinessDataOnDingDing struct {
	ClientID string `json:"client_id"`
	Ticket   string `json:"rm-ticket"`
}

type ReportLogin struct {
	Type     string `json:"type" binding:"required"`
	Data     string `json:"data" binding:"required"`
	UserCode string `json:"user_code"`
	BatchId  string `json:"batch_id"`
	ClientId string `json:"client_id"`
	OrgName  string `json:"org_name"`
}

type LoginReportData struct {
	ClientCurrentPosition   string `json:"client_current_position,omitempty"`    // 终端本次登录的地址
	ClientCurrentNetwork    string `json:"client_current_network,omitempty"`     //终端本次使用的网络
	ClientActiveTime        string `json:"client_active_time,omitempty"`         // 终端的活跃时间
	AccountLogonClientCount int    `json:"account_logon_client_count,omitempty"` // 账号登录终端数量
	ClientAssetsType        string `json:"client_assets_type,omitempty"`         // 终端资产类型
	ClientLogonAccountCount int    `json:"client_logon_account_count,omitempty"` // 终端登录账号数量
	AccountLogonPositions   string `json:"account_logon_positions,omitempty"`    // 账号登录位置
	ClientLogonIntervalTime int    `json:"client_logon_interval_time,omitempty"` // 终端登录间隔时间
	ClientLatelyPosition    string `json:"client_lately_position,omitempty"`     // 终端上次登录的地址

}
